# React 知识库系统开发指南

## 项目概述

这是一个基于React和Node.js的现代化知识库管理系统，具备以下核心功能：

### 主要特性

1. **知识管理**
   - 创建、编辑、删除知识条目
   - 支持Markdown格式
   - 实时预览功能
   - 分类和标签管理

2. **搜索功能**
   - 全文搜索
   - 按分类筛选
   - 按标签筛选

3. **导入导出**
   - Markdown文件导入
   - 批量导入支持
   - PDF导出
   - 合并导出功能

4. **API支持**
   - RESTful API设计
   - 完整的CRUD操作
   - 错误处理机制

## 技术栈

### 前端
- React 19
- TypeScript
- Material-UI
- React Router
- Axios

### 后端
- Node.js
- Express
- JSON文件存储
- Multer (文件上传)
- PDFKit (PDF生成)

## 开发环境搭建

```bash
# 安装后端依赖
cd server
npm install

# 安装前端依赖
cd ../client
npm install

# 启动后端服务
cd ../server
npm start

# 启动前端服务
cd ../client
npm run dev
```

## API接口文档

### 知识条目API

#### 获取所有知识条目
```
GET /api/knowledge
```

#### 创建知识条目
```
POST /api/knowledge
Content-Type: application/json

{
  "title": "标题",
  "content": "内容",
  "category_id": 1,
  "tags": [1, 2]
}
```

#### 更新知识条目
```
PUT /api/knowledge/:id
```

#### 删除知识条目
```
DELETE /api/knowledge/:id
```

### 导出API

#### 导出为Markdown
```
GET /api/export/markdown/:id
```

#### 导出为PDF
```
GET /api/export/pdf/:id
```

### 导入API

#### 导入Markdown文件
```
POST /api/import/markdown
Content-Type: multipart/form-data
```

## 使用说明

1. **创建知识条目**：点击右下角的"+"按钮
2. **编辑知识条目**：在列表中点击编辑按钮
3. **搜索知识**：使用顶部搜索框
4. **导入文件**：访问"导入导出"页面
5. **导出内容**：在知识条目菜单中选择导出选项

## 最佳实践

- 使用有意义的标题
- 合理使用分类和标签
- 定期备份数据
- 保持内容结构清晰

## 故障排除

### 常见问题

1. **服务器启动失败**
   - 检查端口是否被占用
   - 确认依赖是否正确安装

2. **前端无法连接后端**
   - 检查后端服务是否运行
   - 确认API地址配置正确

3. **文件上传失败**
   - 检查文件格式是否支持
   - 确认文件大小不超过限制

## 扩展功能

未来可以考虑添加的功能：

- 用户认证和权限管理
- 协作编辑功能
- 版本控制
- 全文索引搜索
- 数据库支持
- 云存储集成

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 许可证

MIT License
