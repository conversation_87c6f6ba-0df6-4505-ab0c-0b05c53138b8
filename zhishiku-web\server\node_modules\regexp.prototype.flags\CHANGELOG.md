# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.5.4](https://github.com/es-shims/RegExp.prototype.flags/compare/v1.5.3...v1.5.4) - 2025-01-02

### Commits

- [Refactor] use `get-proto` and `es-errors` [`6355772`](https://github.com/es-shims/RegExp.prototype.flags/commit/6355772344db0465e11c58e387f968de0cf4e4fa)
- [Deps] update `call-bind` [`c630183`](https://github.com/es-shims/RegExp.prototype.flags/commit/c6301838b07f3e71b3dc1d5b2cfa88f88b9f6a6d)
- [<PERSON> <PERSON><PERSON>] update `object-inspect` [`c0201af`](https://github.com/es-shims/RegExp.prototype.flags/commit/c0201af97cb849947d5246fa7d9f9a8ea5bc5b36)

## [v1.5.3](https://github.com/es-shims/RegExp.prototype.flags/compare/v1.5.2...v1.5.3) - 2024-10-03

### Fixed

- [Fix] avoid unnecessary polyfill [`#31`](https://github.com/es-shims/RegExp.prototype.flags/issues/31)

### Commits

- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`9e8bc20`](https://github.com/es-shims/RegExp.prototype.flags/commit/9e8bc20868a6366c08cfa9f37648573b8bcab449)
- [Dev Deps] update `@es-shims/api`, `auto-changelog`, `es-value-fixtures`, `tape` [`c32be8e`](https://github.com/es-shims/RegExp.prototype.flags/commit/c32be8e8bb8b6726376ba82d27a31087b4847444)
- [readme] fix typos [`195c32e`](https://github.com/es-shims/RegExp.prototype.flags/commit/195c32e7f48d09b12428442aaaa5a28a72d56287)
- [Deps] update `call-bind`, `set-function-name` [`207517f`](https://github.com/es-shims/RegExp.prototype.flags/commit/207517f62aa8f6a9c721c6d484c2c7f5a386864a)
- [Dev Deps] update `available-regexp-flags`, `tape` [`b3854f7`](https://github.com/es-shims/RegExp.prototype.flags/commit/b3854f70afa7a5da8c0f4cac40bb8ad8df71d297)
- [Tests] replace `aud` with `npm audit` [`d3ee951`](https://github.com/es-shims/RegExp.prototype.flags/commit/d3ee951c0760b3f9f01e5a953dfef92c8d6fcfb4)
- [Dev Deps] update `hasown` [`9e407c4`](https://github.com/es-shims/RegExp.prototype.flags/commit/9e407c41cd8db52b1fd602ae3117b3cd3e1beecc)
- [Dev Deps] add missing peer dep [`0199bfd`](https://github.com/es-shims/RegExp.prototype.flags/commit/0199bfd69dd140bdee743d6a874d758349aaaaaa)

## [v1.5.2](https://github.com/es-shims/RegExp.prototype.flags/compare/v1.5.1...v1.5.2) - 2024-02-11

### Commits

- [Tests] increase coverage [`c692c88`](https://github.com/es-shims/RegExp.prototype.flags/commit/c692c88e073f6d17fc5b6b1c740ef1ecb433eb4b)
- [Dev Deps] use `hasown` instead of `has` [`fb5b350`](https://github.com/es-shims/RegExp.prototype.flags/commit/fb5b350122507e4bec68fb417a46f07d062caf77)
- [Dev Deps] update `aud`, `hasown`, `npmignore`, `tape` [`fd0ddd9`](https://github.com/es-shims/RegExp.prototype.flags/commit/fd0ddd9c837c0a8066de37a41eb4f83310f15a57)
- [Deps] update `call-bind`, `define-properties`, `set-function-name` [`ca53f66`](https://github.com/es-shims/RegExp.prototype.flags/commit/ca53f662549b2fbff61323a6d2aa9af15a6c836b)
- [Dev Deps] update `object-inspect`, `tape` [`4491680`](https://github.com/es-shims/RegExp.prototype.flags/commit/449168010d4d63a2cd854dcec9f37efacda19082)
- [Refactor] use `es-errors` [`1d03d22`](https://github.com/es-shims/RegExp.prototype.flags/commit/1d03d225327008aeb6b4b501a4720318d717621e)
- [Fix] properly check for a non-object receiver [`024d442`](https://github.com/es-shims/RegExp.prototype.flags/commit/024d4421edd8922f9acbac35afcfe69a8a3db6a9)
- [Deps] update `call-bind` [`f222ce0`](https://github.com/es-shims/RegExp.prototype.flags/commit/f222ce0925a7431d1d3dd113d803746f07e1175d)

## [v1.5.1](https://github.com/es-shims/RegExp.prototype.flags/compare/v1.5.0...v1.5.1) - 2023-09-12

### Commits

- [Refactor] use `set-function-name` [`1384147`](https://github.com/es-shims/RegExp.prototype.flags/commit/13841474baf5700de8e3f65fae3670ad1b233483)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `available-regexp-flags`, `tape` [`07bf9a2`](https://github.com/es-shims/RegExp.prototype.flags/commit/07bf9a2c354cc41379b01fe5c383233adaaccf0e)
- [Dev Deps] add missing `npmignore` dep [`8ca9dfe`](https://github.com/es-shims/RegExp.prototype.flags/commit/8ca9dfe4da31ef74ee48a4dcf62692eebb8f8fdd)

## [v1.5.0](https://github.com/es-shims/RegExp.prototype.flags/compare/v1.4.3...v1.5.0) - 2023-04-18

### Commits

- [meta] use `npmignore` to autogenerate an npmignore file [`f7438ad`](https://github.com/es-shims/RegExp.prototype.flags/commit/f7438ad3728128b99daaeb1d3133a40d906d3621)
- [New] add `unicodeSets`/`v` flag [`f922170`](https://github.com/es-shims/RegExp.prototype.flags/commit/f92217039c9c86a61421ce69594e93a546721397)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `available-regexp-flags`, `object-inspect`, `tape` [`1203078`](https://github.com/es-shims/RegExp.prototype.flags/commit/12030785e6beb1f06c406a4731714012d93867e2)
- [actions] update rebase action to use reusable workflow [`c562ea2`](https://github.com/es-shims/RegExp.prototype.flags/commit/c562ea20c27442ff30ce65091a313b5e49ae8515)
- [Dev Deps] update `aud`, `object-inspect`, `tape` [`f3ae811`](https://github.com/es-shims/RegExp.prototype.flags/commit/f3ae81109dd08b200dce8bd52c183bc44662c3b4)
- [Deps] update `define-properties`, `functions-have-names` [`2d0476e`](https://github.com/es-shims/RegExp.prototype.flags/commit/2d0476e5eba1e4a9b786f169f3be96c2f5a192b2)
- [Tests] use `for-each` instead of `foreach` [`d9f30da`](https://github.com/es-shims/RegExp.prototype.flags/commit/d9f30dab65ff0185a8688c6e62dc1fe650879cc0)
- [Deps] update `define-properties` [`81c1c20`](https://github.com/es-shims/RegExp.prototype.flags/commit/81c1c2012070c1f4480a9ab55bc0cff206152603)

## [v1.4.3](https://github.com/es-shims/RegExp.prototype.flags/compare/v1.4.2...v1.4.3) - 2022-04-14

### Commits

- [Fix] when shimmed, name must be `get flags` [`fcefd00`](https://github.com/es-shims/RegExp.prototype.flags/commit/fcefd0039177e9cbcb2ed842d353131ace7a3377)

## [v1.4.2](https://github.com/es-shims/RegExp.prototype.flags/compare/v1.4.1...v1.4.2) - 2022-04-12

### Commits

- [Fix] ensure `hasIndices` is patched properly, and getter order is correct [`a1af45a`](https://github.com/es-shims/RegExp.prototype.flags/commit/a1af45a8a6f7305b097b83f96ee9fc45abb3e733)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog`, `tape` [`24f5a0c`](https://github.com/es-shims/RegExp.prototype.flags/commit/24f5a0c84f2e7d263ae0e2008def870afd6d5a4f)

## [v1.4.1](https://github.com/es-shims/RegExp.prototype.flags/compare/v1.4.0...v1.4.1) - 2022-01-13

### Commits

- [Fix] `polyfill`: do not throw in a descriptorless environment [`e2d24e7`](https://github.com/es-shims/RegExp.prototype.flags/commit/e2d24e707a44d958a0b6d3a114effb2f2b475337)

## [v1.4.0](https://github.com/es-shims/RegExp.prototype.flags/compare/v1.3.2...v1.4.0) - 2022-01-13

### Commits

- [Tests] use `available-regexp-flags` [`95af246`](https://github.com/es-shims/RegExp.prototype.flags/commit/95af2463f1373282087528f8566e20ffae26c3db)
- [New] add `hasIndices`/`d` flag [`89959ca`](https://github.com/es-shims/RegExp.prototype.flags/commit/89959ca1128ea48dcd0ec1416355264425fa3bc5)

## [v1.3.2](https://github.com/es-shims/RegExp.prototype.flags/compare/v1.3.1...v1.3.2) - 2022-01-13

### Commits

- [actions] reuse common workflows [`6665b5d`](https://github.com/es-shims/RegExp.prototype.flags/commit/6665b5db7c45ce6b987d08ebaf6d2767eec95b94)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`babce94`](https://github.com/es-shims/RegExp.prototype.flags/commit/babce94b5ca96e93e74e384c0a01295943677a3f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `object-inspect`, `safe-publish-latest`, `tape` [`52132d9`](https://github.com/es-shims/RegExp.prototype.flags/commit/52132d9f3df904864d4cf3fd44892ee563aee524)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `aud`, `auto-changelog`, `object-inspect`, `tape` [`c16687c`](https://github.com/es-shims/RegExp.prototype.flags/commit/c16687c118d374d8997a8d885467507bf943b4bc)
- [actions] update codecov uploader [`0a3c904`](https://github.com/es-shims/RegExp.prototype.flags/commit/0a3c904a9fd1247b3b8e0fb6b451b3fbe97735bd)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`3fce7f2`](https://github.com/es-shims/RegExp.prototype.flags/commit/3fce7f27c753440003675d03ae9a7ecfa6a74d30)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`75ca498`](https://github.com/es-shims/RegExp.prototype.flags/commit/75ca49889349fc42e51ea79b2ec7a1996fb3eb18)
- [actions] update workflows [`300f321`](https://github.com/es-shims/RegExp.prototype.flags/commit/300f321984526066656bec791f0bb3861b33cfbc)
- [meta] better `eccheck` command [`5f735ab`](https://github.com/es-shims/RegExp.prototype.flags/commit/5f735ab1b1c87dbd05c0096249160587f166cd51)
- [Dev Deps] update `eslint`, `tape` [`3059637`](https://github.com/es-shims/RegExp.prototype.flags/commit/3059637210eb5c9fa97160ec2f0aea1d1d724eb7)
- [actions] update workflows` [`dbd8ab4`](https://github.com/es-shims/RegExp.prototype.flags/commit/dbd8ab49fa2196dd74791107825c43e4481cdfd2)
- [meta] use `prepublishOnly` script for npm 7+ [`5cc8652`](https://github.com/es-shims/RegExp.prototype.flags/commit/5cc86524a41bf358b6701bcf46e480f0e3e470b4)
- [Fix] use polyfill, not implementation, in main export [`15ab4b8`](https://github.com/es-shims/RegExp.prototype.flags/commit/15ab4b85f3904e48664e26394dc12765ed666da4)
- [meta] remove `audit-level` config, which breaks npm 7 installs [`1cb98ae`](https://github.com/es-shims/RegExp.prototype.flags/commit/1cb98aed731e73d11df5ed3b853b371d35a69f5a)

## [v1.3.1](https://github.com/es-shims/RegExp.prototype.flags/compare/v1.3.0...v1.3.1) - 2021-01-15

### Commits

- [Tests] run `nyc` on all tests; use `tape` runner; add full es-shims test suite [`047a1e8`](https://github.com/es-shims/RegExp.prototype.flags/commit/047a1e8ff250220254b0e9598d962a56c8ec3ffc)
- [Tests] migrate tests to Github Actions [`e4e391f`](https://github.com/es-shims/RegExp.prototype.flags/commit/e4e391fd3e6f057172994ad0c33ca128568c0b06)
- [meta] use `auto-changelog` for changelog [`afbcd06`](https://github.com/es-shims/RegExp.prototype.flags/commit/afbcd06402e97e975af797e2c1375e35e22e90f2)
- [actions] add Require Allow Edits workflow [`0db5d50`](https://github.com/es-shims/RegExp.prototype.flags/commit/0db5d50cdf59e3e5529024af4f8ce05829edc06d)
- [meta] do not publish github action workflow files [`53f2902`](https://github.com/es-shims/RegExp.prototype.flags/commit/53f29020e5a1f517e91b8cf226ed6bc97eadc090)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape`; add `aud` [`05f2a85`](https://github.com/es-shims/RegExp.prototype.flags/commit/05f2a851869069c7911176809028be8491465f86)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`2a197b8`](https://github.com/es-shims/RegExp.prototype.flags/commit/2a197b84916f094946c5cad56ef8e7bb7e8f12ac)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape`; add `safe-publish-latest` [`e40bd37`](https://github.com/es-shims/RegExp.prototype.flags/commit/e40bd37de9bb756672832a6c994652965d09b9ae)
- [Refactor] use `call-bind` instead of `es-abstract` [`e6eac90`](https://github.com/es-shims/RegExp.prototype.flags/commit/e6eac9052ebdb4bc28cb83b5d3017a4ed74fe3f1)
- [Deps] update `es-abstract` [`f198075`](https://github.com/es-shims/RegExp.prototype.flags/commit/f198075d6fc075e0d98967af98a512742e6e7e4f)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`2d21727`](https://github.com/es-shims/RegExp.prototype.flags/commit/2d217275d78214b82c7f5cacca85ca2308df83f1)
- [Deps] update `es-abstract` [`7e7ddc6`](https://github.com/es-shims/RegExp.prototype.flags/commit/7e7ddc66174256f6688a857b09c9a02bafcf4866)

## [v1.3.0](https://github.com/es-shims/RegExp.prototype.flags/compare/v1.2.0...v1.3.0) - 2019-12-14

### Commits

- [Tests] remove `jscs` [`4a09ab4`](https://github.com/es-shims/RegExp.prototype.flags/commit/4a09ab467f62065a1718b0dcc50f7818b5400ab6)
- [Tests] use shared travis-ci configs [`8afa6a9`](https://github.com/es-shims/RegExp.prototype.flags/commit/8afa6a99fd35c19fb49ba630fd17159a5da2a34e)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `covert`, `has`, `tape` [`13a9fc9`](https://github.com/es-shims/RegExp.prototype.flags/commit/13a9fc9d6bc2600681eb3f638668beccf80b843c)
- [Refactor] use `callBind` helper from `es-abstract` [`c3a3727`](https://github.com/es-shims/RegExp.prototype.flags/commit/c3a37276764d99c1e4f7e9467ad636fce8c92c58)
- [actions] add automatic rebasing / merge commit blocking [`51e3f93`](https://github.com/es-shims/RegExp.prototype.flags/commit/51e3f9366d15a07edaf532884948ce74b6827125)
- [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops [`7e1ee50`](https://github.com/es-shims/RegExp.prototype.flags/commit/7e1ee505df374867c2c04d500aa1c36265161b6f)
- [meta] add `funding` field [`c99cbec`](https://github.com/es-shims/RegExp.prototype.flags/commit/c99cbec1af9b0e0be42e82f164adacf2e1bdee16)
- [New] add `auto` entry point [`1e53e85`](https://github.com/es-shims/RegExp.prototype.flags/commit/1e53e854f663472e74dd0350e0c095df9c2b9c7b)
- [Tests] use `eclint` instead of `editorconfig-tools` [`8600bfe`](https://github.com/es-shims/RegExp.prototype.flags/commit/8600bfed42ab8d294463df482874c344fc079f82)
- [Deps] update `define-properties` [`ad221fa`](https://github.com/es-shims/RegExp.prototype.flags/commit/ad221fa2a26a9c2bc8d274b689cf7a626b58f4e9)

## [v1.2.0](https://github.com/es-shims/RegExp.prototype.flags/compare/v1.1.1...v1.2.0) - 2017-10-24

### Commits

- [Tests] up to `node` `v8.8`, `v7.10`, `v6.11`, `v4.8`; improve matrix; use `nvm install-latest-npm` so new npm doesn’t break old node [`5a9653d`](https://github.com/es-shims/RegExp.prototype.flags/commit/5a9653d1904eb8ad8baffe43cd065b6f36013e5a)
- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config`; add `has` [`556de86`](https://github.com/es-shims/RegExp.prototype.flags/commit/556de8632bbe7a23279717f7d0b6ee841514fbe1)
- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config` [`726772c`](https://github.com/es-shims/RegExp.prototype.flags/commit/726772c054a499ab7680823c4bd8fa9b048d9420)
- [New] add support for `dotAll` regex flag. [`fcbd64f`](https://github.com/es-shims/RegExp.prototype.flags/commit/fcbd64f84fd974d98384bdb093bf25656eb72e8f)
- [Dev Deps] update `eslint`, `jscs`, `nsp`, `tape`, `@ljharb/eslint-config`, `@es-shims/api` [`0272934`](https://github.com/es-shims/RegExp.prototype.flags/commit/02729344addadc105b9c5e12d90cca85a75d16d6)
- [Dev Deps] update `jscs`, `nsp`, `eslint` [`e4cd264`](https://github.com/es-shims/RegExp.prototype.flags/commit/e4cd264f4afa33ff865325b04791de95696e3ae4)
- [Dev Deps] update `jscs`, `nsp`, `eslint`, `@es-shims/api` [`baf5169`](https://github.com/es-shims/RegExp.prototype.flags/commit/baf51698ac00b31b6a4a6d5646a183a409ad1118)
- [Dev Deps] update `tape`, `nsp`, `eslint`, `@ljharb/eslint-config` [`97cea15`](https://github.com/es-shims/RegExp.prototype.flags/commit/97cea152c20bb0e63e9c5111780f7b4af5d1a0e8)
- [Dev Deps] update `tape`, `discs`, `eslint`, `@ljharb/eslint-config` [`b6872f4`](https://github.com/es-shims/RegExp.prototype.flags/commit/b6872f44c833f6f7faf63881657208b6cd43ef49)
- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config` [`14702cc`](https://github.com/es-shims/RegExp.prototype.flags/commit/14702ccd050029d4e6ea2e59d0912e6bfc16ffc0)
- [Dev Deps] update `jscs`, `@es-shims/api` [`cd060a6`](https://github.com/es-shims/RegExp.prototype.flags/commit/cd060a650db019be5244e1c1b77a29f6d79c89db)
- [Tests] up to `node` `v6.2`, `v5.11` [`14638bd`](https://github.com/es-shims/RegExp.prototype.flags/commit/14638bdbd62d6b6a7c89efb8ec57a7815032b4bb)
- [Tests] up to `io.js` `v3.3`, `node` `v4.1` [`b0a5ffb`](https://github.com/es-shims/RegExp.prototype.flags/commit/b0a5ffb25a76783053652e0d7f835e354f9b29b6)
- [Tests] npm run silently [`35804d4`](https://github.com/es-shims/RegExp.prototype.flags/commit/35804d45dd7f57faab923aaab914e6390813e700)
- [Tests] up to `node` `v5.9`, `v4.4` [`e0fe80d`](https://github.com/es-shims/RegExp.prototype.flags/commit/e0fe80d96783820444d6dea1e6b5739032a50c1b)
- [Tests] up to `node` `v5.7`, `v4.3` [`9739c42`](https://github.com/es-shims/RegExp.prototype.flags/commit/9739c422523571cc439d73a9ecaf5dc2e2643bec)
- [Dev Deps] update `jscs` [`4aa1699`](https://github.com/es-shims/RegExp.prototype.flags/commit/4aa1699a0582b7739f14c6cd8d5ae1a4515bd604)
- [Dev Deps] update `tape`, `jscs`, `nsp`, `@ljharb/eslint-config` [`8bc5e6b`](https://github.com/es-shims/RegExp.prototype.flags/commit/8bc5e6ba5befc8f399e00f3c2d064519457fb57c)
- [Tests] fix npm upgrades on older nodes [`ae00bb9`](https://github.com/es-shims/RegExp.prototype.flags/commit/ae00bb9d979605f41fc598156b5c590923ac8184)
- Only apps should have lockfiles. [`6d14965`](https://github.com/es-shims/RegExp.prototype.flags/commit/6d1496550a962ea8525fb7b62dc4ac99d9513a6d)
- [Tests] use pretest/posttest for better organization [`0520cfd`](https://github.com/es-shims/RegExp.prototype.flags/commit/0520cfda23835fb5bff038a6e5cc530b0ce66985)
- [Tests] up to `node` `v5.5` [`810f62b`](https://github.com/es-shims/RegExp.prototype.flags/commit/810f62b6d2418e843b7c2c225841e9305dbc01ee)
- [Tests] on `node` `v5.3` [`f839662`](https://github.com/es-shims/RegExp.prototype.flags/commit/f839662887cbb1a5e472a9302185355b431c85c1)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config` [`78ecaa5`](https://github.com/es-shims/RegExp.prototype.flags/commit/78ecaa5b203a07f76505824f77ce1e5d60d8b0ca)
- [Tests] up to `node` `v5.2` [`c04d762`](https://github.com/es-shims/RegExp.prototype.flags/commit/c04d762a8c09ab544df14c14521f32dac3f67823)
- [Tests] up to `node` `v5.0` [`7c0d5b9`](https://github.com/es-shims/RegExp.prototype.flags/commit/7c0d5b944d9ba30f38227d0750109d582be254e2)
- [Tests] on `node` `v5.10` [`40ddafd`](https://github.com/es-shims/RegExp.prototype.flags/commit/40ddafd83e2e1c959ee8ba24cb296559f2545a0c)
- [Deps] update `define-properties` [`98ea89d`](https://github.com/es-shims/RegExp.prototype.flags/commit/98ea89dc9c41b81b84d4071105048687dab0660e)

## [v1.1.1](https://github.com/es-shims/RegExp.prototype.flags/compare/v1.1.0...v1.1.1) - 2015-08-16

### Commits

- [Fix] cover the case where there is no descriptor on the prototype [`67014c3`](https://github.com/es-shims/RegExp.prototype.flags/commit/67014c35a93c76e28c4ab5cd3e5a54f7f40c2ddf)

## [v1.1.0](https://github.com/es-shims/RegExp.prototype.flags/compare/v1.0.1...v1.1.0) - 2015-08-16

### Commits

- Update `jscs`, `eslint`; use my personal shared `eslint` config. [`37ca379`](https://github.com/es-shims/RegExp.prototype.flags/commit/37ca379bc72620fa6785b0a9ca791b160328c236)
- Update `eslint`, `tape`, `editorconfig-tools`, `nsp` [`cb92d6e`](https://github.com/es-shims/RegExp.prototype.flags/commit/cb92d6e8a8c1df5f00a226e11a78f38c6f7c3055)
- Implement the [es-shim API](es-shims/api). [`15eb821`](https://github.com/es-shims/RegExp.prototype.flags/commit/15eb821be2771e03a1341a08483513702118b45c)
- Refactoring to reduce complexity. [`aeb4785`](https://github.com/es-shims/RegExp.prototype.flags/commit/aeb47854f6b00355702104066c63f6eed38b5e81)
- Move implementation to `implementation.js` [`a698925`](https://github.com/es-shims/RegExp.prototype.flags/commit/a698925b4c1c78cd1ed4315b9deb5bb1707d5203)
- Update `eslint`, `jscs` [`277a4a1`](https://github.com/es-shims/RegExp.prototype.flags/commit/277a4a15e663eb823b63743b84645158b9bb9a43)
- Update `nsp`, `eslint` [`c9f3866`](https://github.com/es-shims/RegExp.prototype.flags/commit/c9f3866e25b52050f6bfe3fd0de8849de0271ea4)
- Update `tape`, `eslint` [`a08795b`](https://github.com/es-shims/RegExp.prototype.flags/commit/a08795b688b186fa5a2ec207358d81c16a07d30d)
- Make some things a bit more robust. [`450abb4`](https://github.com/es-shims/RegExp.prototype.flags/commit/450abb48974f10bfd2d9478e7ea1b9d87f004fb9)
- Update `eslint` [`25d898f`](https://github.com/es-shims/RegExp.prototype.flags/commit/25d898f62719b26fea5f9245be141103d4ec58cd)
- Test on latest two `io.js` versions. [`2e17ca3`](https://github.com/es-shims/RegExp.prototype.flags/commit/2e17ca304e12fb5071a091706a4d559b3eac968a)
- All grade A-supported `node`/`iojs` versions now ship with an `npm` that understands `^`. [`4a2a548`](https://github.com/es-shims/RegExp.prototype.flags/commit/4a2a5480c50f30814000684462a8a3b44c87ae2e)
- Update `eslint` [`64df4e0`](https://github.com/es-shims/RegExp.prototype.flags/commit/64df4e0a2d0e2901b57652e30913db797dc0829b)
- Update `eslint` [`ac05ae5`](https://github.com/es-shims/RegExp.prototype.flags/commit/ac05ae509a4a70d107820a749ea6f02784fc41eb)
- Clean up `supportsDescriptors` check. [`e44d0de`](https://github.com/es-shims/RegExp.prototype.flags/commit/e44d0dec9c8415ff9a911b8806e1d245d6919a11)
- [Dev Deps] Update `jscs` [`8741758`](https://github.com/es-shims/RegExp.prototype.flags/commit/87417588f52f1176fc37d7c32221aa85f749aa34)
- Update `tape`, `jscs`, `nsp`, `eslint` [`db1f658`](https://github.com/es-shims/RegExp.prototype.flags/commit/db1f6584b18cc035ef3b5aec556f54e0ee8c639d)
- Test on `io.js` `v2.3` [`18c948f`](https://github.com/es-shims/RegExp.prototype.flags/commit/18c948f033c87ab2657a0395052cbec531c40900)
- Run `travis-ci` tests on `iojs` and `node` v0.12; speed up builds; allow 0.8 failures. [`c37e79f`](https://github.com/es-shims/RegExp.prototype.flags/commit/c37e79f380d87a226a6cdaa5f09f832f5dc21b7d)
- Update `tape`, `jscs`, `eslint` [`4b652bf`](https://github.com/es-shims/RegExp.prototype.flags/commit/4b652bf5f2f0e36a15227d0b4048de91ee6c4433)
- [Dev Deps] Update `tape`, `eslint` [`29d4ac0`](https://github.com/es-shims/RegExp.prototype.flags/commit/29d4ac0bea16c6a9f611cb15baccd30449f30a91)
- Test up to `io.js` `v2.1` [`9f9e342`](https://github.com/es-shims/RegExp.prototype.flags/commit/9f9e34295ced1b288dea08e0a66dffd2bc03ff8b)
- Update `covert`, `jscs` [`c98f3b4`](https://github.com/es-shims/RegExp.prototype.flags/commit/c98f3b47f01f317e8a589486dfaee482c66b8b64)
- Update `jscs` [`9e5e220`](https://github.com/es-shims/RegExp.prototype.flags/commit/9e5e220be6ec5d5b9b658235287e35bded580b06)
- [Dev Deps] update `tape` [`cdd3af2`](https://github.com/es-shims/RegExp.prototype.flags/commit/cdd3af21507b01aa524f8b87f158dfc8a8153c85)
- [Dev Deps] update `tape` [`d42d0bf`](https://github.com/es-shims/RegExp.prototype.flags/commit/d42d0bf28f8da2cb47fff49283a07a693f8cb626)
- Switch from vb.teelaun.ch to versionbadg.es for the npm version badge SVG. [`a5e7453`](https://github.com/es-shims/RegExp.prototype.flags/commit/a5e745375c01e9f90ff632c55a5b44b6ada38217)
- Update `tape` [`2a675ec`](https://github.com/es-shims/RegExp.prototype.flags/commit/2a675ec707a9d89aea403d0b9a723ea531e50c2d)
- Test on `io.js` `v2.5` [`448cbdb`](https://github.com/es-shims/RegExp.prototype.flags/commit/448cbdb7df47e52677daea4e0c41e892ad8770e4)
- Test on `io.js` `v2.4` [`948e511`](https://github.com/es-shims/RegExp.prototype.flags/commit/948e51129c01147ffe4dedc3a7d4980128d0cf73)
- Test on `io.js` `v2.2` [`4793278`](https://github.com/es-shims/RegExp.prototype.flags/commit/4793278f5aca187e36b42b08fc1388d8021400e2)
- Update `eslint` [`0f463da`](https://github.com/es-shims/RegExp.prototype.flags/commit/0f463daa14a193ed94b16c46832074d63e861c91)
- Update `eslint` [`5a16967`](https://github.com/es-shims/RegExp.prototype.flags/commit/5a16967db71bb8a24c81a27ee366f0b02b663e34)
- Test on `io.js` `v3.0` [`7ba8706`](https://github.com/es-shims/RegExp.prototype.flags/commit/7ba87064bc8520d34a9560bea8e366d70c93dbbb)
- Test on `iojs-v1.2` [`b521e09`](https://github.com/es-shims/RegExp.prototype.flags/commit/b521e099b7de48cfbdd6860265eb5e972d2859a5)

## [v1.0.1](https://github.com/es-shims/RegExp.prototype.flags/compare/v1.0.0...v1.0.1) - 2014-12-13

### Merged

- Match the spec properly: throw when not an object; make getter generic. [`#3`](https://github.com/es-shims/RegExp.prototype.flags/pull/3)

### Fixed

- Match the spec properly [`#1`](https://github.com/es-shims/RegExp.prototype.flags/issues/1)

### Commits

- Speed up the “is object” check in case of `null` or `undefined` [`77137f9`](https://github.com/es-shims/RegExp.prototype.flags/commit/77137f99449c9b6583cdfda295a00b832dfd45f3)

## v1.0.0 - 2014-12-10

### Commits

- Adding dotfiles [`313812e`](https://github.com/es-shims/RegExp.prototype.flags/commit/313812e1d8ff42a13dbc8689f2e719324c46c9ca)
- Tests [`625a042`](https://github.com/es-shims/RegExp.prototype.flags/commit/625a042220a3152b49608fb6f187f67bff02b6fb)
- Add package.json [`8b98257`](https://github.com/es-shims/RegExp.prototype.flags/commit/8b98257f900d0a73c8eb3805b9b01999e05e880a)
- Adding the README [`884798b`](https://github.com/es-shims/RegExp.prototype.flags/commit/884798b710d5a85bc6d9a6879f509766e2e57c0e)
- Implementation. [`4186cc9`](https://github.com/es-shims/RegExp.prototype.flags/commit/4186cc9d9a7533f78d88be976f0a8a2757604fc5)
- Adding LICENSE and CHANGELOG [`f87fa81`](https://github.com/es-shims/RegExp.prototype.flags/commit/f87fa8126cc6c39747fbe0dc6cb40ca0ff77fbbc)
- Fixing README URLs [`b821703`](https://github.com/es-shims/RegExp.prototype.flags/commit/b821703d5e5b01ee4f526f15c8e525645cf95ef7)
- Clean up dependencies; update `tape`, `jscs`, `nsp` [`0e13fc1`](https://github.com/es-shims/RegExp.prototype.flags/commit/0e13fc12df09f3a7ac30116ef13bba820c220730)
- Initial commit. [`8a9e35e`](https://github.com/es-shims/RegExp.prototype.flags/commit/8a9e35e15f65c9640e64ee14fab190a60993efaa)
