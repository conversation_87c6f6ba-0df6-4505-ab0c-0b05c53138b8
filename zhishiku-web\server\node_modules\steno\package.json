{"name": "steno", "version": "3.2.0", "description": "Specialized fast async file writer", "keywords": ["fs", "file", "write", "writer", "asynchronous", "fast", "race", "condition", "atomic", "writing", "safe"], "homepage": "https://github.com/typicode/steno", "bugs": {"url": "https://github.com/typicode/steno/issues"}, "repository": {"type": "git", "url": "git+https://github.com/typicode/steno.git"}, "funding": "https://github.com/sponsors/typicode", "license": "MIT", "author": "Typicode <<EMAIL>>", "type": "module", "exports": "./lib/index.js", "types": "lib/index.d.ts", "files": ["lib/index.js", "lib/index.d.ts"], "scripts": {"test": "node --import tsx/esm --test src/test.ts", "build": "del-cli lib && tsc", "lint": "eslint src --ext .ts --ignore-path .gitignore", "prepare": "husky install", "prepublishOnly": "npm run build", "postversion": "git push && git push --tags && npm publish", "benchmark": "npm run build && node lib/benchmark.js", "commit": "commit"}, "devDependencies": {"@commitlint/cli": "^17.7.2", "@commitlint/config-conventional": "^17.7.0", "@commitlint/prompt-cli": "^17.7.2", "@sindresorhus/tsconfig": "^5.0.0", "@types/node": "^20.8.3", "@typicode/eslint-config": "^1.2.0", "del-cli": "^5.1.0", "husky": "^8.0.3", "tsx": "^4.7.0", "typescript": "^5.2.2"}, "engines": {"node": ">=16"}}