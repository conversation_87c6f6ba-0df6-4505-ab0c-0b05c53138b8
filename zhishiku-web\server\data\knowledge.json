{"items": [{"id": "63dcc093-ca6c-4985-b56c-db1c8ab676f0", "title": "NAS同步工具套件", "content": "NAS同步工具套件是一个跨平台的解决方案，专为高效、安全地在本地计算机和网络附加存储(NAS)设备之间同步文件而设计。本套件包含三个主要组件：服务器、客户端和桥接工具，支持双向同步、增量同步、差量同步、自动调度以及详细的文件对比功能。\n\n## 核心特性\n\n### 同步功能\n- **双向同步**：支持从本地到NAS和从NAS到本地的双向文件同步\n- **增量同步**：智能识别变更文件，仅传输有修改的文件，节省时间和带宽\n- **差量同步**：仅传输文件中被修改的部分，而非整个文件，大幅提升大文件更新效率\n- **自动调度**：基于灵活的定时表达式自动执行同步任务\n- **实时进度跟踪**：展示详细的同步进度、当前处理文件和预计完成时间\n- **任务进度条**：直观显示同步任务进度，支持暂停和取消操作\n- **深度扫描**：支持深度目录扫描选项，提供更准确的文件计数和进度估算\n\n### 跨平台兼容性\n- **全平台支持**：服务器端支持Linux、Windows、macOS\n- **桥接工具**：提供Windows、macOS和Linux桌面应用，允许远程访问本地文件系统\n- **客户端**：提供Web界面，可从任何设备访问\n\n### 高级功能\n- **文件对比**：在同步前直观展示源目录和目标目录的差异\n- **目录浏览**：内置文件浏览器，轻松选择同步目录\n- **安全传输**：支持HTTPS加密传输，确保数据安全\n- **错误处理**：提供详细日志和错误报告，便于排查\n- **用户友好界面**：直观简洁的界面设计，易于配置和监控同步任务\n\n## 系统组件\n\n### 服务器 (nas-sync-server)\n服务器组件负责管理同步任务、调度任务执行和维护任务状态。它提供REST API供客户端和桥接工具交互，处理文件传输和目录对比逻辑。\n\n**主要功能**：\n- 同步任务管理\n- 文件传输和对比\n- 进度跟踪\n- 调度管理\n- 数据库存储\n\n### 客户端 (nas-sync-client)\n客户端是一个基于Web的用户界面，允许用户创建、管理和监控同步任务。它与服务器通信，展示同步进度和结果。\n\n**主要功能**：\n- 任务创建和管理\n- 实时进度显示\n- 目录浏览\n- 任务调度配置\n- 历史记录查看\n\n### 桥接工具 (nas-sync-bridge)\n桥接工具是一个Electron桌面应用，可安装在本地计算机上，提供HTTP/HTTPS接口访问本地文件系统。它使NAS设备能够通过网络访问本地文件，实现双向同步。\n\n**主要功能**：\n- 本地文件系统访问\n- 安全认证\n- 文件操作API\n- 系统托盘集成\n- 自动启动\n\n## 安装指南\n\n### 服务器安装\n\n```bash\n# 导航到服务器目录\ncd nas-sync-server\n\n# 安装依赖\nnpm install\n\n# 初始化数据库\nnpm run init-db\n\n# 启动服务器\nnpm start\n```\n\n服务器默认在3000端口启动。可通过`.env`文件配置端口和数据库连接参数。\n\n### 客户端安装\n\n```bash\n# 导航到客户端目录\ncd nas-sync-client\n\n# 安装依赖\nnpm install\n\n# 启动开发服务器\nnpm start\n```\n\n客户端默认在3001端口启动。可通过`.env`文件配置API服务器地址。\n\n### 桥接工具安装\n\n```bash\n# 导航到桥接工具目录\ncd nas-sync-bridge\n\n# 安装依赖\nnpm install\n\n# 启动应用\nnpm start\n\n# 构建可执行文件(可选)\nnpm run build\n```\n\n桥接工具默认在8000端口提供HTTP服务。可通过应用界面配置端口和认证信息。\n\n### Windows安装注意事项\n\n1. 首先安装MySQL：\n   - 从[MySQL官网](https://dev.mysql.com/downloads/installer/)下载安装程序\n   - 安装MySQL Server 8.0或更高版本\n   - 选择\"Server only\"或\"Custom\"安装类型\n   - 配置类型选择\"Development Computer\"\n   - 身份验证方法选择\"Use Legacy Authentication Method\"\n   - 记录安装时设置的root密码\n   - 确保MySQL服务已启动\n\n2. 安装Node.js：\n   - 从[Node.js官网](https://nodejs.org/)下载LTS版本\n   - 按照安装向导完成安装\n   - 验证安装：`node -v` 和 `npm -v`\n\n3. 配置环境：\n   - 在服务器目录创建`.env`文件\n   - 设置数据库连接参数和服务器配置\n\n```\n# .env示例\nDB_HOST=localhost\nDB_USER=root\nDB_PASSWORD=你的MySQL密码\nDB_NAME=nas_sync\nPORT=3000\nLOG_LEVEL=info\n```\n\n4. 初始化数据库：\n   - 打开命令提示符或PowerShell\n   - 进入服务器目录：`cd path\\to\\server`\n   - 运行初始化脚本：`npm run init-db`\n\n5. 运行服务：\n   - 启动服务器：`npm start`\n   - 启动客户端：进入客户端目录执行 `npm start`\n   - 启动桥接工具：进入桥接工具目录执行 `npm start`\n\n## 差量同步功能\n\n差量同步（Differential Sync）是一项高级功能，能够只传输文件中实际修改的部分，而不是整个文件，特别适用于大文件小改动的场景。通过块级别差异检测算法，显著降低网络传输量和同步时间。\n\n### 主要优势\n\n- **大幅减少数据传输量**：对于大型文件（如数据库、日志文件等）的小改动，传输量可降低超过90%\n- **加速同步过程**：节省传输时间，特别是在带宽有限的环境中\n- **降低网络负载**：减轻网络负担，降低带宽占用\n- **适用于特定场景**：特别适合文档编辑、日志追加、配置文件修改等场景\n\n### 工作原理\n\n差量同步使用基于rsync算法思想的块级别检测技术：\n\n1. **文件分块**：将文件分割为固定大小的数据块（默认64KB）\n2. **校验和计算**：计算每个块的弱校验和（Adler-32）和强校验和（MD5）\n3. **块匹配**：将源文件与目标文件的块进行比较，找出匹配和不匹配的部分\n4. **差异传输**：仅传输源文件中在目标文件不存在的块数据\n5. **文件重组**：在目标位置重新组装文件，保留目标文件中未修改的部分\n\n### 使用方法\n\n在创建同步任务时，简单勾选\"使用差量同步\"选项即可启用此功能：\n\n1. 在Web界面中的\"新建同步\"中，找到\"使用差量同步\"选项\n2. 勾选此选项将为适用的文件启用差量同步\n3. 系统会自动判断文件大小，并对大文件（>128KB）应用差量同步\n4. 小文件将自动降级为标准复制，避免不必要的开销\n\n### 性能优化\n\n差量同步功能包含多项性能优化措施：\n\n- **智能降级**：小文件（<128KB）自动使用全量复制，避免算法开销\n- **并行处理**：使用工作线程池并行处理数据块\n- **流式传输**：使用流处理大文件，减少内存占用\n- **错误恢复**：自动从差量同步失败恢复，降级到全量复制\n- **内存优化**：分批处理文件，避免内存占用过大\n\n## 配置说明\n\n### 服务器配置\n服务器通过`.env`文件配置，主要参数包括：\n- `PORT`: 服务器监听端口\n- `DB_HOST`: 数据库主机地址\n- `DB_USER`: 数据库用户名\n- `DB_PASSWORD`: 数据库密码\n- `DB_NAME`: 数据库名称\n- `LOG_LEVEL`: 日志级别 (debug, info, warn, error)\n- `MAX_CONCURRENT_TASKS`: 最大并发任务数\n- `FILE_CHUNK_SIZE`: 文件传输块大小\n\n### 客户端配置\n客户端通过`.env`文件配置，主要参数包括：\n- `REACT_APP_API_URL`: 服务器API地址\n- `REACT_APP_POLLING_INTERVAL`: 任务状态轮询间隔(毫秒)\n- `REACT_APP_MAX_UPLOAD_SIZE`: 最大上传文件大小(MB)\n- `REACT_APP_THEME`: 界面主题 (light, dark, system)\n- `REACT_APP_DEFAULT_SCAN_DEPTH`: 默认扫描深度 (1-4)\n\n### 桥接工具配置\n桥接工具通过图形界面配置，主要设置包括：\n- 服务器端口\n- 是否启用HTTPS\n- 访问认证（用户名/密码）\n- 共享目录和权限\n- 启动选项\n\n## 常见问题\n\n### 同步任务失败\n- **问题**: 任务开始后很快失败，无进度更新\n- **解决方案**: \n  - 检查源目录和目标目录是否存在且有访问权限\n  - 验证网络连接是否稳定\n  - 查看服务器日志获取详细错误信息\n\n### 路径解析错误\n- **问题**: 创建任务时出现\"路径解析错误\"\n- **解决方案**:\n  - Windows路径使用正斜杠(/)或双反斜杠(\\\\\\\\)\n  - 使用绝对路径而非相对路径\n  - 确保桥接工具正确配置且正在运行\n\n### 数据库连接问题\n- **问题**: 服务器启动时报告数据库连接错误\n- **解决方案**:\n  - 确认MySQL服务正在运行\n  - 验证`.env`文件中的数据库配置是否正确\n  - 检查数据库用户是否有足够权限\n\n### UI加载缓慢\n- **问题**: 客户端界面加载时间过长\n- **解决方案**:\n  - 检查服务器是否正常运行\n  - 减少活跃任务数量\n  - 考虑增加服务器资源\n\n### 进度估算不准确\n- **问题**: 同步进度显示不准确或跳跃\n- **解决方案**:\n  - 启用深度扫描选项获得更准确的文件计数\n  - 对于大型目录，增加扫描深度值\n  - 理解进度显示为估算值，可能有一定误差\n\n### 差量同步问题\n- **问题**: 差量同步执行后文件出现损坏\n- **解决方案**:\n  - 检查源文件和目标文件是否可能被其他进程修改\n  - 尝试使用传统同步模式，关闭差量同步选项\n  - 在服务器日志中查找详细错误信息\n\n## 使用技巧\n\n### 增量同步最佳实践\n- 首次同步后使用增量模式减少传输时间\n- 对于频繁更改的大型目录尤为有效\n- 配合调度功能实现自动增量更新\n\n### 差量同步应用场景\n- 大型数据库文件定期备份(如SQLite、Access文件)\n- 持续添加内容的日志文件同步\n- 文档编辑后的高效更新\n- 大型Excel表格或PowerPoint演示文稿\n- 虚拟机镜像文件的同步\n\n### 排除规则\n- 使用正则表达式排除不需要同步的文件\n- 常见排除模式：`node_modules/`, `*.tmp`, `.git/`\n- 可为每个任务单独设置排除规则\n\n### 任务分组\n- 将相关任务组合成任务组\n- 按类型或重要性分组便于管理\n- 使用标签功能标记和筛选任务\n\n### 状态通知\n- 配置任务完成后的邮件通知\n- 使用Webhook集成第三方服务\n- 重要任务失败时获取即时通知\n\n### 备份最佳实践\n- 定期备份配置和任务数据\n- 将重要数据同步到多个目标位置\n- 使用版本控制功能跟踪文件变更\n\n## 版本历史\n\n### v0.5.0 (2024-09-15)\n- 添加差量同步功能，支持块级别增量更新\n- 优化文件校验和计算算法\n- 增强同步进度跟踪和估算\n\n### v0.4.0 (2024-09-05)\n- 添加深度扫描功能，支持更准确的文件计数\n- 优化Windows路径处理，解决特殊字符问题\n- 改进桥接工具UI，支持深色主题\n- 添加同步任务进度详细显示\n- 优化文件比较算法，提高性能\n- 修复文件浏览器在处理大目录时的性能问题\n- 增强错误处理和日志记录\n\n### v0.3.0 (2024-06-20)\n- 添加Windows安装指南\n- 优化数据库连接配置\n- 改进任务进度监控功能\n- 添加深度扫描选项，提高文件计数准确性\n- 修复Windows路径处理问题\n- 增强用户界面错误处理\n- 改进文件浏览器性能\n\n### v0.2.0 (2024-07-10)\n- 添加调度功能\n- 桥接工具初版\n- 性能优化\n\n### v0.1.0 (2024-05-05)\n- 增加增量同步功能\n- 改进错误处理\n- UI优化\n\n### v0.0.1 (2024-02-15)\n- 初始版本发布\n- 基本同步功能\n- 简单的Web界面\n\n## 参与贡献\n\n我们欢迎社区贡献，无论是功能增强、错误修复还是文档改进。请遵循以下步骤参与项目：\n\n1. Fork项目仓库\n2. 创建功能分支 (`git checkout -b feature/amazing-feature`)\n3. 提交更改 (`git commit -m 'Add some amazing feature'`)\n4. 推送分支 (`git push origin feature/amazing-feature`)\n5. 创建Pull Request\n\n## 联系方式\n\n- **项目维护者**: 张三\n- **邮箱**: <EMAIL>\n- **问题反馈**: [GitHub Issues](https://github.com/yourusername/nas-sync-tool/issues)\n- **社区讨论**: [GitHub Discussions](https://github.com/yourusername/nas-sync-tool/discussions)", "category_id": 1, "tags": [], "created_at": "2025-06-19T15:28:51.659Z", "updated_at": "2025-06-19T15:28:51.659Z"}], "categories": [{"id": 1, "name": "默认分类", "description": "系统默认分类", "created_at": "2025-06-19T14:42:52.300Z", "updated_at": "2025-06-19T14:42:52.300Z"}], "tags": [{"id": 1, "name": "重要", "color": "#f44336", "created_at": "2025-06-19T14:42:52.300Z"}, {"id": 2, "name": "学习", "color": "#2196f3", "created_at": "2025-06-19T14:42:52.300Z"}, {"id": 3, "name": "工作", "color": "#4caf50", "created_at": "2025-06-19T14:42:52.300Z"}, {"id": 4, "name": "技术", "color": "#ff9800", "created_at": "2025-06-19T14:42:52.300Z"}, {"id": 5, "name": "笔记", "color": "#9c27b0", "created_at": "2025-06-19T14:42:52.300Z"}], "nextCategoryId": 2, "nextTagId": 6}